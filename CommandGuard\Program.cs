// 引入Microsoft.Extensions.Configuration命名空间，用于配置管理

using System.Diagnostics;
using CommandGuard.Configuration;
using CommandGuard.Data;
using CommandGuard.Forms;
using CommandGuard.Interfaces.Repositories;
using CommandGuard.Interfaces.Services;
using CommandGuard.Repositories;
using CommandGuard.Services;
using FreeSql;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Settings.Configuration;

// 引入Microsoft.Extensions.DependencyInjection命名空间，用于依赖注入
// 引入Microsoft.Extensions.Logging命名空间，用于日志记录
// 引入Serilog命名空间，用于结构化日志记录
// 引入System.Diagnostics命名空间，用于性能监控
// 引入窗体命名空间，用于主窗体

namespace CommandGuard;

/// <summary>
/// 应用程序入口点类，负责配置依赖注入、日志记录和启动主窗体
///
/// 主要职责：
/// 1. 初始化WinForms应用程序配置
/// 2. 构建配置管理系统（从appsettings.json读取）
/// 3. 设置依赖注入容器并注册所有服务
/// 4. 配置全局异常处理机制
/// 5. 启动主窗体并运行消息循环
///
/// 架构特点：
/// - 使用现代化的.NET依赖注入模式
/// - 支持配置文件热重载
/// - 集成Serilog结构化日志
/// - 完善的异常处理和资源清理
/// </summary>
static class Program
{
    #region 字段

    /// <summary>
    /// 依赖注入服务提供者
    /// 管理整个应用程序的服务生命周期，包括数据库连接、仓储、服务层等
    /// 在应用程序结束时需要正确释放以避免资源泄漏
    /// </summary>
    private static ServiceProvider? _serviceProvider;

    /// <summary>
    /// 启动性能监控计时器
    /// 用于监控应用程序启动各阶段的耗时，帮助识别性能瓶颈
    /// </summary>
    private static readonly Stopwatch StartupStopwatch = new();

    #endregion

    #region 主入口方法

    /// <summary>
    /// 应用程序主入口点
    /// 负责初始化应用程序、配置依赖注入、设置异常处理并启动主窗体
    /// 优化后的启动流程确保异常处理覆盖全过程，日志记录从早期开始，并包含性能监控
    /// </summary>
    [STAThread] // 设置为单线程单元模式，WinForms应用程序必需
    static void Main()
    {
        // 启动性能监控计时器
        StartupStopwatch.Start();

        try
        {
            // 第1步：初始化WinForms应用程序的基本配置
            // 包括高DPI支持、视觉样式等现代化设置
            ApplicationConfiguration.Initialize();
            LogStartupStep("WinForms配置初始化完成");

            // 第2步：尽早设置全局异常处理，确保后续所有步骤的异常都能被捕获
            SetupGlobalExceptionHandling();
            LogStartupStep("全局异常处理设置完成");

            // 第3步：构建应用程序配置对象，从appsettings.json读取配置
            var configuration = BuildConfiguration();
            LogStartupStep("应用程序配置构建完成");

            // 第4步：验证关键配置项，确保应用程序能够正常运行
            ValidateConfiguration(configuration);
            LogStartupStep("配置验证完成");

            // 第5步：初始化基础日志记录，使后续步骤能够记录详细日志
            InitializeEarlyLogging(configuration);
            LogStartupStep("基础日志记录初始化完成");

            // 第6步：创建依赖注入服务容器
            var services = new ServiceCollection();
            LogStartupStep("依赖注入容器创建完成");

            // 第7步：配置所有应用程序服务到DI容器中
            ConfigureServices(services, configuration);
            LogStartupStep("服务注册完成");

            // 第8步：构建服务提供者，完成依赖注入容器的初始化
            _serviceProvider = services.BuildServiceProvider();
            LogStartupStep("服务提供者构建完成");

            // 第9步：验证关键服务是否正确注册
            ValidateEssentialServices();
            LogStartupStep("关键服务验证完成");

            // 第10步：获取完整的日志服务并记录启动进度
            var logger = GetStartupLogger();
            logger.LogInformation("应用程序核心服务初始化完成，准备启动主窗体");

            // 第11步：从DI容器获取主窗体实例（已注入所需的服务）
            var mainForm = _serviceProvider.GetRequiredService<FormMain>();
            LogStartupStep("主窗体创建完成");

            // 第12步：记录启动完成信息和性能数据
            LogStartupCompletion(logger);

            // 第13步：启动WinForms消息循环，显示主窗体
            Application.Run(mainForm);
        }
        catch (Exception ex)
        {
            // 记录致命错误到Serilog日志
            Log.Fatal(ex, @"应用程序启动失败");

            // 向用户显示启动失败的错误信息
            MessageBox.Show(@$"应用程序启动失败: {ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            // 确保Serilog日志缓冲区被刷新并关闭
            Log.CloseAndFlush();

            // 释放依赖注入容器及其管理的所有服务资源
            _serviceProvider?.Dispose();
        }
    }

    #endregion

    #region 启动辅助方法

    /// <summary>
    /// 设置全局异常处理
    /// 尽早设置异常处理，确保整个启动过程中的异常都能被捕获
    /// </summary>
    private static void SetupGlobalExceptionHandling()
    {
        // 配置全局异常处理模式，捕获所有未处理的异常
        Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);

        // 注册UI线程异常处理事件
        Application.ThreadException += Application_ThreadException;

        // 注册应用程序域异常处理事件（处理非UI线程异常）
        AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
    }

    /// <summary>
    /// 验证关键配置项
    /// 确保应用程序运行所需的基本配置都存在且有效
    /// </summary>
    /// <param name="configuration">配置对象</param>
    private static void ValidateConfiguration(IConfiguration configuration)
    {
        // 验证配置对象本身不为空
        if (configuration == null)
        {
            throw new InvalidOperationException("配置对象构建失败");
        }

        // 验证数据库连接字符串（如果配置中没有，会使用默认值）
        var connectionString = configuration.GetConnectionString("DefaultConnection");
        if (string.IsNullOrWhiteSpace(connectionString))
        {
            // 这里不抛异常，因为我们有默认的SQLite连接字符串
            Console.WriteLine("警告：未找到数据库连接字符串配置，将使用默认SQLite数据库");
        }

        // 可以添加更多配置验证逻辑
        // 例如：验证日志配置、API密钥等
    }

    /// <summary>
    /// 初始化早期日志记录
    /// 在依赖注入容器构建之前就开始记录日志，便于调试启动过程
    /// </summary>
    /// <param name="configuration">配置对象</param>
    private static void InitializeEarlyLogging(IConfiguration configuration)
    {
        // 简化的Serilog配置，避免单文件发布时的程序集加载问题
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Information()
            .MinimumLevel.Override(@"Microsoft", Serilog.Events.LogEventLevel.Warning)
            .MinimumLevel.Override(@"System", Serilog.Events.LogEventLevel.Warning)
            .Enrich.FromLogContext()
            .WriteTo.File(@"logs/startup-.txt",
                rollingInterval: RollingInterval.Day,
                retainedFileCountLimit: 7,
                outputTemplate: @"{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
            .WriteTo.File(@"logs/app-.log",
                rollingInterval: RollingInterval.Day,
                retainedFileCountLimit: 7,
                outputTemplate: @"{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
            .CreateLogger();

        // 记录早期启动日志
        Log.Information("应用程序启动开始，启动时间：{StartTime}", DateTime.Now);
    }

    /// <summary>
    /// 验证关键服务是否正确注册
    /// 确保核心服务都能从DI容器中正确获取
    /// </summary>
    private static void ValidateEssentialServices()
    {
        if (_serviceProvider == null)
        {
            throw new InvalidOperationException("服务提供者未初始化");
        }

        // 验证日志工厂服务
        var loggerFactory = _serviceProvider.GetService<ILoggerFactory>();
        if (loggerFactory == null)
        {
            throw new InvalidOperationException("日志工厂服务注册失败");
        }

        // 验证配置服务
        var config = _serviceProvider.GetService<IConfiguration>();
        if (config == null)
        {
            throw new InvalidOperationException("配置服务注册失败");
        }

        // 验证主窗体服务
        var mainForm = _serviceProvider.GetService<FormMain>();
        if (mainForm == null)
        {
            throw new InvalidOperationException("主窗体服务注册失败");
        }

        // 可以添加更多关键服务的验证
    }

    /// <summary>
    /// 获取启动专用的日志记录器
    /// 从完整的DI容器中获取日志服务
    /// </summary>
    /// <returns>日志记录器</returns>
    private static Microsoft.Extensions.Logging.ILogger GetStartupLogger()
    {
        var loggerFactory = _serviceProvider!.GetRequiredService<ILoggerFactory>();
        return loggerFactory.CreateLogger("Program.Startup");
    }

    /// <summary>
    /// 记录启动步骤和性能信息
    /// 用于监控启动过程中各个步骤的耗时
    /// </summary>
    /// <param name="stepName">步骤名称</param>
    private static void LogStartupStep(string stepName)
    {
        var elapsed = StartupStopwatch.ElapsedMilliseconds;
        Log.Information("启动步骤完成：{StepName}，累计耗时：{ElapsedMs}ms", stepName, elapsed);
    }

    /// <summary>
    /// 记录启动完成信息和性能统计
    /// 提供完整的启动性能报告
    /// </summary>
    /// <param name="logger">日志记录器</param>
    private static void LogStartupCompletion(Microsoft.Extensions.Logging.ILogger logger)
    {
        StartupStopwatch.Stop();
        var totalTime = StartupStopwatch.ElapsedMilliseconds;

        logger.LogInformation("应用程序启动完成！");
        logger.LogInformation("启动性能报告：");
        logger.LogInformation("- 总启动时间：{TotalTime}ms", totalTime);
        logger.LogInformation("- 启动时间：{StartTime}", DateTime.Now.AddMilliseconds(-totalTime));
        logger.LogInformation("- 完成时间：{CompletionTime}", DateTime.Now);

        // 如果启动时间过长，记录警告
        if (totalTime > 5000) // 5秒
        {
            logger.LogWarning("应用程序启动时间较长：{TotalTime}ms，建议检查启动性能", totalTime);
        }
    }

    #endregion

    #region 配置方法

    /// <summary>
    /// 构建应用程序配置对象
    /// 从appsettings.json文件读取配置信息，支持热重载
    /// </summary>
    /// <returns>包含应用程序所有配置信息的IConfiguration对象</returns>
    private static IConfiguration BuildConfiguration()
    {
        return new ConfigurationBuilder()
            // 设置配置文件的基础路径为应用程序所在目录
            .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
            // 添加JSON配置文件，必需存在，支持运行时重新加载
            .AddJsonFile(@"appsettings.json", optional: false, reloadOnChange: true)
            // 构建最终的配置对象
            .Build();
    }

    /// <summary>
    /// 配置依赖注入服务
    /// 注册应用程序所需的所有服务到DI容器中，包括配置、日志、业务服务和UI组件
    /// </summary>
    /// <param name="services">Microsoft.Extensions.DependencyInjection服务集合容器</param>
    /// <param name="configuration">应用程序配置对象，包含appsettings.json中的所有配置</param>
    private static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // 将配置对象注册为单例服务，整个应用程序生命周期内共享同一个实例
        // 其他服务可以通过构造函数注入IConfiguration来访问配置信息
        services.AddSingleton(configuration);

        // 配置Serilog结构化日志记录系统
        // 调用扩展方法设置日志输出格式、目标和级别
        services.AddLogging(configuration);

        // 注册应用程序核心业务服务
        // 包括：FreeSql ORM、数据库上下文、仓储层、服务层
        // 调用扩展方法统一配置所有业务相关的服务
        services.AddApplicationServices(configuration);

        // 注册主窗体为瞬态服务（每次请求创建新实例）
        // 瞬态生命周期适合UI组件，确保每次获取都是新的窗体实例
        services.AddTransient<FormMain>();
    }

    /// <summary>
    /// 添加应用程序业务服务到DI容器
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置对象</param>
    /// <returns>服务集合</returns>
    private static void AddApplicationServices(this IServiceCollection services, IConfiguration configuration)
    {
        // 配置强类型设置对象，支持配置热重载和验证
        services.Configure<DatabaseSettings>(configuration.GetSection(DatabaseSettings.SectionName));
        services.Configure<ApplicationSettings>(configuration.GetSection(ApplicationSettings.SectionName));

        // 配置FreeSql ORM框架
        var connectionString = configuration.GetConnectionString(@"DefaultConnection") ?? @"Data Source=database.db";
        var freeSql = new FreeSqlBuilder()
            .UseConnectionString(DataType.Sqlite, connectionString)
            .UseAutoSyncStructure(true) // 自动同步实体结构到数据库
            .UseNoneCommandParameter(true) // 不使用参数化查询（适用于简单场景）
            .Build();

        // 注册FreeSql为单例服务
        services.AddSingleton(freeSql);

        // 注册应用程序数据库上下文
        services.AddScoped<AppDbContext>();

        // 注册仓储层服务（数据访问层）
        services.AddScoped<IPersonRepository, PersonRepository>();

        // 注册业务服务层
        services.AddScoped<IPersonService, PersonService>();
        services.AddScoped<IConfigurationService, ConfigurationService>();
    }

    /// <summary>
    /// 配置Serilog结构化日志记录
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置对象</param>
    /// <returns>服务集合</returns>
    private static void AddLogging(this IServiceCollection services, IConfiguration configuration)
    {
        // 简化的Serilog配置，避免单文件发布时的程序集加载问题
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Information()
            .MinimumLevel.Override(@"Microsoft", Serilog.Events.LogEventLevel.Warning)
            .MinimumLevel.Override(@"System", Serilog.Events.LogEventLevel.Warning)
            .Enrich.FromLogContext()
            .WriteTo.File(@"logs/app-.log",
                rollingInterval: RollingInterval.Day,
                retainedFileCountLimit: 7,
                outputTemplate: @"{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
            .CreateLogger();

        // 配置.NET日志系统使用Serilog
        services.AddLogging(builder =>
        {
            builder.ClearProviders(); // 清除默认日志提供者
            builder.AddSerilog(); // 添加Serilog作为日志提供者
        });
    }

    #endregion

    #region 异常处理方法

    /// <summary>
    /// 处理UI线程未捕获的异常
    /// 当WinForms UI线程中发生未被捕获的异常时，此方法会被调用
    /// 主要处理用户界面操作中的异常，如按钮点击、控件事件等
    /// </summary>
    /// <param name="sender">触发异常事件的对象，通常是Application对象</param>
    /// <param name="e">包含异常详细信息的事件参数</param>
    private static void Application_ThreadException(object sender, ThreadExceptionEventArgs e)
    {
        // 尝试从依赖注入容器获取日志工厂服务
        // 使用可空操作符防止在服务提供者未初始化时出错
        var loggerFactory = _serviceProvider?.GetService<ILoggerFactory>();

        // 创建专用于程序级别异常处理的日志记录器
        var logger = loggerFactory?.CreateLogger("Program");

        // 记录异常详细信息到日志系统，包括异常堆栈跟踪
        logger?.LogError(e.Exception, @"未处理的线程异常");

        // 向用户显示友好的错误提示对话框
        // 只显示异常消息，避免暴露技术细节给最终用户
        MessageBox.Show(@$"发生错误: {e.Exception.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
    }

    /// <summary>
    /// 处理应用程序域未捕获的异常
    /// 当非UI线程（如后台线程、任务线程）中发生未被捕获的异常时，此方法会被调用
    /// 这类异常通常比UI线程异常更严重，可能导致应用程序崩溃
    /// </summary>
    /// <param name="sender">触发异常事件的对象，通常是AppDomain对象</param>
    /// <param name="e">包含异常对象和终止信息的事件参数</param>
    private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        // 尝试从依赖注入容器获取日志工厂服务
        // 在应用程序即将崩溃时，服务提供者可能已经被释放
        var loggerFactory = _serviceProvider?.GetService<ILoggerFactory>();

        // 创建专用于严重异常处理的日志记录器
        var logger = loggerFactory?.CreateLogger("Program");

        // 将异常对象转换为Exception类型并记录为严重错误
        // 使用LogCritical级别表示这是可能导致应用程序终止的严重问题
        logger?.LogCritical((Exception)e.ExceptionObject, @"未处理的应用程序域异常");

        // 向用户显示严重错误提示对话框
        // 明确标识为"严重错误"，提醒用户这是系统级问题
        MessageBox.Show(@$"发生严重错误: {((Exception)e.ExceptionObject).Message}", @"严重错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
    }

    #endregion
}