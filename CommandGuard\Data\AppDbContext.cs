using CommandGuard.Models;
using FreeSql;

namespace CommandGuard.Data;

/// <summary>
/// 应用程序数据库上下文，提供对所有实体的数据访问
/// </summary>
public class AppDbContext
{
    #region 属性

    /// <summary>FreeSql实例，提供数据库操作接口</summary>
    private IFreeSql FreeSql { get; }

    #endregion

    #region 构造函数

    /// <summary>
    /// 构造函数，初始化数据库上下文
    /// </summary>
    /// <param name="freeSql">FreeSql实例</param>
    public AppDbContext(IFreeSql freeSql)
    {
        FreeSql = freeSql;

        // 初始化数据库结构，确保所有表都存在
        InitializeDatabase();
    }

    #endregion

    #region 实体操作接口

    // Person 实体操作
    /// <summary>人员查询接口</summary>
    public ISelect<Person> Persons => FreeSql.Select<Person>();
    /// <summary>人员插入接口</summary>
    public IInsert<Person> InsertPerson => FreeSql.Insert<Person>();
    /// <summary>人员更新接口</summary>
    public IUpdate<Person> UpdatePerson => FreeSql.Update<Person>();
    /// <summary>人员删除接口</summary>
    public IDelete<Person> DeletePerson => FreeSql.Delete<Person>();



    #endregion

    #region 通用操作方法

    /// <summary>
    /// 获取指定实体类型的查询接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>查询接口</returns>
    public ISelect<T> Select<T>() where T : class => FreeSql.Select<T>();

    /// <summary>
    /// 获取指定实体类型的插入接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>插入接口</returns>
    public IInsert<T> Insert<T>() where T : class => FreeSql.Insert<T>();

    /// <summary>
    /// 获取指定实体类型的更新接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>更新接口</returns>
    public IUpdate<T> Update<T>() where T : class => FreeSql.Update<T>();

    /// <summary>
    /// 获取指定实体类型的删除接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>删除接口</returns>
    public IDelete<T> Delete<T>() where T : class => FreeSql.Delete<T>();

    #endregion

    #region 数据库初始化
    /// <summary>
    /// 初始化数据库结构
    /// </summary>
    private void InitializeDatabase()
    {
        // 同步所有实体的数据库结构，确保表存在且字段匹配
        SyncEntityStructure<Person>();

        // 未来添加新实体时，在这里添加同步调用
        // SyncEntityStructure<User>();
        // SyncEntityStructure<Product>();
    }

    /// <summary>
    /// 同步指定实体的数据库结构
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    private void SyncEntityStructure<T>() where T : class
    {
        try
        {
            // 使用FreeSql的CodeFirst功能自动创建或更新表结构
            FreeSql.CodeFirst.SyncStructure<T>();
        }
        catch (Exception ex)
        {
            // 数据库结构同步失败时抛出详细异常信息
            throw new InvalidOperationException(@$"同步实体 {typeof(T).Name} 的数据库结构失败", ex);
        }
    }
    #endregion
}
