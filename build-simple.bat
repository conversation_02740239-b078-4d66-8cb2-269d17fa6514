@echo off
echo Building single file version...

dotnet publish CommandGuard\CommandGuard.csproj -c Release -r win-x64 --self-contained false -p:PublishSingleFile=true -o publish\SingleFile

echo Build completed!
echo Output directory: publish\SingleFile\
if exist "publish\SingleFile\CommandGuard.exe" (
    echo Main file: CommandGuard.exe
    dir "publish\SingleFile\CommandGuard.exe"
) else (
    echo Error: CommandGuard.exe not found
)

pause
