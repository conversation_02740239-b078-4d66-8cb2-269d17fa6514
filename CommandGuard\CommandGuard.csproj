﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net8.0-windows</TargetFramework>
        <Nullable>enable</Nullable>
        <UseWindowsForms>true</UseWindowsForms>
        <ImplicitUsings>enable</ImplicitUsings>
        <ApplicationIcon>Bitdefender.ico</ApplicationIcon>
    </PropertyGroup>

    <!-- 发布时自动启用单文件配置 -->
    <PropertyGroup Condition="'$(Configuration)' == 'Release'">
        <PublishSingleFile>true</PublishSingleFile>
        <SelfContained>false</SelfContained>
        <PublishReadyToRun>false</PublishReadyToRun>
        <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
        <DebugType>none</DebugType>
        <DebugSymbols>false</DebugSymbols>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="FreeSql" Version="3.5.212" />
      <PackageReference Include="FreeSql.Provider.Sqlite" Version="3.5.212" />
      <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.8" />
      <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.8" />
      <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.8" />
      <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.8" />
      <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.8" />
      <PackageReference Include="Serilog" Version="4.3.0" />
      <PackageReference Include="Serilog.Extensions.Logging" Version="9.0.2" />
      <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
      <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    </ItemGroup>

    <!-- 发布时排除不必要的文件 -->
    <ItemGroup>
      <Content Remove="**/*.pdb" />
      <Content Remove="**/*.xml" />
    </ItemGroup>

    <!-- 修剪配置 -->
    <ItemGroup>
      <TrimmerRootAssembly Include="CommandGuard" />
      <TrimmerRootAssembly Include="System.Windows.Forms" />
      <TrimmerRootAssembly Include="FreeSql" />
      <TrimmerRootAssembly Include="FreeSql.Provider.Sqlite" />
    </ItemGroup>

    <ItemGroup>
      <None Update="appsettings.json">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>

</Project>