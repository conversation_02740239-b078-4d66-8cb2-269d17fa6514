using CommandGuard.Interfaces.Repositories;
using CommandGuard.Interfaces.Services;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services;

/// <summary>
/// 人员服务实现类，提供Person相关的业务逻辑处理
/// 包含数据验证、业务规则检查和仓储层调用
/// </summary>
public class PersonService : IPersonService
{
    #region 字段

    private readonly IPersonRepository _personRepository;
    private readonly ILogger<PersonService> _logger;

    #endregion

    #region 构造函数

    /// <summary>构造函数，注入依赖服务</summary>
    public PersonService(IPersonRepository personRepository, ILogger<PersonService> logger)
    {
        _personRepository = personRepository;
        _logger = logger;
    }

    #endregion

    #region 查询方法

    /// <summary>
    /// 根据ID获取人员信息
    /// </summary>
    /// <param name="id">人员ID</param>
    /// <returns>匹配的人员信息，未找到或ID无效返回null</returns>
    public async Task<Person?> GetPersonByIdAsync(int id)
    {
        if (id <= 0)
        {
            _logger.LogWarning(@"无效的Person ID: {Id}", id);
            return null;
        }

        return await _personRepository.GetByIdAsync(id);
    }

    /// <summary>
    /// 获取所有人员列表
    /// </summary>
    /// <returns>所有人员的列表</returns>
    public async Task<IEnumerable<Person>> GetAllPersonsAsync()
    {
        return await _personRepository.GetAllAsync();
    }

    /// <summary>
    /// 根据姓名搜索人员
    /// </summary>
    /// <param name="name">姓名关键字</param>
    /// <returns>包含关键字的人员列表</returns>
    public async Task<IEnumerable<Person>> SearchPersonsByNameAsync(string name)
    {
        if (string.IsNullOrWhiteSpace(name))
        {
            return new List<Person>();
        }

        return await _personRepository.SearchByNameAsync(name.Trim());
    }

    /// <summary>
    /// 根据年龄范围获取人员列表
    /// </summary>
    /// <param name="minAge">最小年龄</param>
    /// <param name="maxAge">最大年龄</param>
    /// <returns>符合年龄范围的人员列表</returns>
    public async Task<IEnumerable<Person>> GetPersonsByAgeRangeAsync(int minAge, int maxAge)
    {
        if (minAge < 0 || maxAge < 0 || minAge > maxAge)
        {
            _logger.LogWarning(@"无效的年龄范围: {MinAge}-{MaxAge}", minAge, maxAge);
            return new List<Person>();
        }

        return await _personRepository.GetByAgeRangeAsync(minAge, maxAge);
    }

    /// <summary>
    /// 根据邮箱获取人员信息
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <returns>匹配的人员信息，未找到返回null</returns>
    public async Task<Person?> GetPersonByEmailAsync(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
        {
            return null;
        }

        return await _personRepository.GetByEmailAsync(email.Trim());
    }

    /// <summary>
    /// 获取人员总数
    /// </summary>
    /// <returns>人员总数量</returns>
    public async Task<int> GetPersonCountAsync()
    {
        return await _personRepository.CountAsync();
    }

    #endregion

    #region 修改方法

    /// <summary>
    /// 创建新人员
    /// </summary>
    /// <param name="person">要创建的人员对象</param>
    /// <returns>创建后的人员对象（包含自增ID）</returns>
    public async Task<Person> CreatePersonAsync(Person person)
    {
        ValidatePerson(person);

        if (!await IsEmailUniqueAsync(person.Email))
        {
            throw new InvalidOperationException(@$"邮箱 {person.Email} 已存在");
        }

        _logger.LogInformation(@"创建新Person: {Name}", person.Name);
        return await _personRepository.AddAsync(person);
    }

    /// <summary>
    /// 更新人员信息
    /// </summary>
    /// <param name="person">要更新的人员对象</param>
    /// <returns>更新后的人员对象</returns>
    public async Task<Person> UpdatePersonAsync(Person person)
    {
        if (person.Id <= 0)
        {
            throw new ArgumentException(@"无效的Person ID");
        }

        ValidatePerson(person);

        var existingPerson = await _personRepository.GetByIdAsync(person.Id);
        if (existingPerson == null)
        {
            throw new InvalidOperationException(@$"Person ID {person.Id} 不存在");
        }

        if (!await IsEmailUniqueAsync(person.Email, person.Id))
        {
            throw new InvalidOperationException(@$"邮箱 {person.Email} 已存在");
        }

        _logger.LogInformation(@"更新Person: {Id}", person.Id);
        return await _personRepository.UpdateAsync(person);
    }

    /// <summary>
    /// 删除人员信息
    /// </summary>
    /// <param name="id">要删除的人员ID</param>
    /// <returns>删除成功返回true，否则返回false</returns>
    public async Task<bool> DeletePersonAsync(int id)
    {
        if (id <= 0)
        {
            _logger.LogWarning(@"无效的Person ID: {Id}", id);
            return false;
        }

        var existingPerson = await _personRepository.GetByIdAsync(id);
        if (existingPerson == null)
        {
            _logger.LogWarning(@"尝试删除不存在的Person: {Id}", id);
            return false;
        }

        _logger.LogInformation(@"删除Person: {Id}", id);
        return await _personRepository.DeleteAsync(id);
    }

    #endregion

    #region 验证方法

    /// <summary>
    /// 检查邮箱是否唯一
    /// </summary>
    /// <param name="email">要检查的邮箱地址</param>
    /// <param name="excludeId">排除的人员ID（用于更新时排除自己）</param>
    /// <returns>邮箱唯一返回true，否则返回false</returns>
    public async Task<bool> IsEmailUniqueAsync(string email, int? excludeId = null)
    {
        if (string.IsNullOrWhiteSpace(email))
        {
            return false;
        }

        var existingPerson = await _personRepository.GetByEmailAsync(email.Trim());

        if (existingPerson == null)
        {
            return true;
        }

        return excludeId.HasValue && existingPerson.Id == excludeId.Value;
    }

    /// <summary>
    /// 验证Person实体的业务规则
    /// </summary>
    /// <param name="person">要验证的人员对象</param>
    private static void ValidatePerson(Person person)
    {
        if (person == null)
        {
            throw new ArgumentNullException(nameof(person));
        }

        // 验证姓名
        if (string.IsNullOrWhiteSpace(person.Name))
        {
            throw new ArgumentException(@"姓名不能为空");
        }

        if (person.Name.Length > 100)
        {
            throw new ArgumentException(@"姓名长度不能超过100个字符");
        }

        // 验证年龄
        if (person.Age < 0 || person.Age > 150)
        {
            throw new ArgumentException(@"年龄必须在0-150之间");
        }

        // 验证邮箱
        if (string.IsNullOrWhiteSpace(person.Email))
        {
            throw new ArgumentException(@"邮箱不能为空");
        }

        if (person.Email.Length > 200)
        {
            throw new ArgumentException(@"邮箱长度不能超过200个字符");
        }

        if (!IsValidEmail(person.Email))
        {
            throw new ArgumentException(@"邮箱格式不正确");
        }
    }

    /// <summary>
    /// 验证邮箱格式是否正确
    /// </summary>
    /// <param name="email">要验证的邮箱地址</param>
    /// <returns>格式正确返回true，否则返回false</returns>
    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    #endregion
}
