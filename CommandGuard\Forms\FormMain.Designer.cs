﻿namespace CommandGuard.Forms;

partial class FormMain
{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;

    /// <summary>
    ///  Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }

        base.Dispose(disposing);
    }

    #region Windows Form Designer generated code

    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        dataGridViewPersons = new DataGridView();
        groupBoxPersonInfo = new GroupBox();
        buttonDelete = new Button();
        buttonUpdate = new Button();
        buttonAdd = new Button();
        textBoxEmail = new TextBox();
        textBoxAge = new TextBox();
        textBoxName = new TextBox();
        labelEmail = new Label();
        labelAge = new Label();
        labelName = new Label();
        groupBoxSearch = new GroupBox();
        buttonSearch = new Button();
        textBoxSearch = new TextBox();
        labelSearch = new Label();
        buttonRefresh = new Button();
        statusStrip = new StatusStrip();
        toolStripStatusLabel = new ToolStripStatusLabel();
        buttonTestMultiThread = new Button();
        ((System.ComponentModel.ISupportInitialize)dataGridViewPersons).BeginInit();
        groupBoxPersonInfo.SuspendLayout();
        groupBoxSearch.SuspendLayout();
        statusStrip.SuspendLayout();
        SuspendLayout();
        //
        // dataGridViewPersons
        //
        dataGridViewPersons.AllowUserToAddRows = false;
        dataGridViewPersons.AllowUserToDeleteRows = false;
        dataGridViewPersons.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
        dataGridViewPersons.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
        dataGridViewPersons.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
        dataGridViewPersons.Location = new Point(12, 12);
        dataGridViewPersons.MultiSelect = false;
        dataGridViewPersons.Name = "dataGridViewPersons";
        dataGridViewPersons.ReadOnly = true;
        dataGridViewPersons.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
        dataGridViewPersons.Size = new Size(560, 300);
        dataGridViewPersons.TabIndex = 0;
        dataGridViewPersons.SelectionChanged += DataGridViewPersons_SelectionChanged;
        //
        // groupBoxPersonInfo
        //
        groupBoxPersonInfo.Anchor = AnchorStyles.Top | AnchorStyles.Right;
        groupBoxPersonInfo.Controls.Add(buttonDelete);
        groupBoxPersonInfo.Controls.Add(buttonUpdate);
        groupBoxPersonInfo.Controls.Add(buttonAdd);
        groupBoxPersonInfo.Controls.Add(textBoxEmail);
        groupBoxPersonInfo.Controls.Add(textBoxAge);
        groupBoxPersonInfo.Controls.Add(textBoxName);
        groupBoxPersonInfo.Controls.Add(labelEmail);
        groupBoxPersonInfo.Controls.Add(labelAge);
        groupBoxPersonInfo.Controls.Add(labelName);
        groupBoxPersonInfo.Location = new Point(590, 12);
        groupBoxPersonInfo.Name = "groupBoxPersonInfo";
        groupBoxPersonInfo.Size = new Size(200, 200);
        groupBoxPersonInfo.TabIndex = 1;
        groupBoxPersonInfo.TabStop = false;
        groupBoxPersonInfo.Text = @"人员信息";
        //
        // buttonDelete
        //
        buttonDelete.Location = new Point(125, 160);
        buttonDelete.Name = "buttonDelete";
        buttonDelete.Size = new Size(60, 25);
        buttonDelete.TabIndex = 8;
        buttonDelete.Text = @"删除";
        buttonDelete.UseVisualStyleBackColor = true;
        buttonDelete.Click += ButtonDelete_Click;
        //
        // buttonUpdate
        //
        buttonUpdate.Location = new Point(65, 160);
        buttonUpdate.Name = "buttonUpdate";
        buttonUpdate.Size = new Size(60, 25);
        buttonUpdate.TabIndex = 7;
        buttonUpdate.Text = @"更新";
        buttonUpdate.UseVisualStyleBackColor = true;
        buttonUpdate.Click += ButtonUpdate_Click;
        //
        // buttonAdd
        //
        buttonAdd.Location = new Point(5, 160);
        buttonAdd.Name = "buttonAdd";
        buttonAdd.Size = new Size(60, 25);
        buttonAdd.TabIndex = 6;
        buttonAdd.Text = @"添加";
        buttonAdd.UseVisualStyleBackColor = true;
        buttonAdd.Click += ButtonAdd_Click;
        //
        // textBoxEmail
        //
        textBoxEmail.Location = new Point(50, 120);
        textBoxEmail.Name = "textBoxEmail";
        textBoxEmail.Size = new Size(140, 23);
        textBoxEmail.TabIndex = 5;
        //
        // textBoxAge
        //
        textBoxAge.Location = new Point(50, 80);
        textBoxAge.Name = "textBoxAge";
        textBoxAge.Size = new Size(140, 23);
        textBoxAge.TabIndex = 4;
        //
        // textBoxName
        //
        textBoxName.Location = new Point(50, 40);
        textBoxName.Name = "textBoxName";
        textBoxName.Size = new Size(140, 23);
        textBoxName.TabIndex = 3;
        //
        // labelEmail
        //
        labelEmail.AutoSize = true;
        labelEmail.Location = new Point(10, 123);
        labelEmail.Name = "labelEmail";
        labelEmail.Size = new Size(32, 17);
        labelEmail.TabIndex = 2;
        labelEmail.Text = @"邮箱:";
        //
        // labelAge
        //
        labelAge.AutoSize = true;
        labelAge.Location = new Point(10, 83);
        labelAge.Name = "labelAge";
        labelAge.Size = new Size(32, 17);
        labelAge.TabIndex = 1;
        labelAge.Text = @"年龄:";
        //
        // labelName
        //
        labelName.AutoSize = true;
        labelName.Location = new Point(10, 43);
        labelName.Name = "labelName";
        labelName.Size = new Size(32, 17);
        labelName.TabIndex = 0;
        labelName.Text = @"姓名:";
        //
        // groupBoxSearch
        //
        groupBoxSearch.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
        groupBoxSearch.Controls.Add(buttonSearch);
        groupBoxSearch.Controls.Add(textBoxSearch);
        groupBoxSearch.Controls.Add(labelSearch);
        groupBoxSearch.Location = new Point(12, 330);
        groupBoxSearch.Name = "groupBoxSearch";
        groupBoxSearch.Size = new Size(400, 60);
        groupBoxSearch.TabIndex = 2;
        groupBoxSearch.TabStop = false;
        groupBoxSearch.Text = @"搜索";
        //
        // buttonSearch
        //
        buttonSearch.Location = new Point(320, 25);
        buttonSearch.Name = "buttonSearch";
        buttonSearch.Size = new Size(60, 25);
        buttonSearch.TabIndex = 2;
        buttonSearch.Text = @"搜索";
        buttonSearch.UseVisualStyleBackColor = true;
        buttonSearch.Click += ButtonSearch_Click;
        //
        // textBoxSearch
        //
        textBoxSearch.Location = new Point(80, 25);
        textBoxSearch.Name = "textBoxSearch";
        textBoxSearch.Size = new Size(230, 23);
        textBoxSearch.TabIndex = 1;
        //
        // labelSearch
        //
        labelSearch.AutoSize = true;
        labelSearch.Location = new Point(10, 28);
        labelSearch.Name = "labelSearch";
        labelSearch.Size = new Size(68, 17);
        labelSearch.TabIndex = 0;
        labelSearch.Text = @"按姓名搜索:";
        //
        // buttonRefresh
        //
        buttonRefresh.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
        buttonRefresh.Location = new Point(430, 355);
        buttonRefresh.Name = "buttonRefresh";
        buttonRefresh.Size = new Size(80, 30);
        buttonRefresh.TabIndex = 3;
        buttonRefresh.Text = @"刷新";
        buttonRefresh.UseVisualStyleBackColor = true;
        buttonRefresh.Click += ButtonRefresh_Click;
        //
        // statusStrip
        //
        statusStrip.Items.AddRange(new ToolStripItem[] { toolStripStatusLabel });
        statusStrip.Location = new Point(0, 428);
        statusStrip.Name = "statusStrip";
        statusStrip.Size = new Size(800, 22);
        statusStrip.TabIndex = 4;
        statusStrip.Text = @"statusStrip1";
        //
        // toolStripStatusLabel
        //
        toolStripStatusLabel.Name = "toolStripStatusLabel";
        toolStripStatusLabel.Size = new Size(56, 17);
        toolStripStatusLabel.Text = @"就绪";
        //
        // buttonTestMultiThread
        //
        buttonTestMultiThread.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
        buttonTestMultiThread.Location = new Point(520, 355);
        buttonTestMultiThread.Name = "buttonTestMultiThread";
        buttonTestMultiThread.Size = new Size(100, 30);
        buttonTestMultiThread.TabIndex = 5;
        buttonTestMultiThread.Text = @"多线程测试";
        buttonTestMultiThread.UseVisualStyleBackColor = true;
        buttonTestMultiThread.Click += ButtonTestMultiThread_Click;
        //
        // FormMain
        //
        AutoScaleDimensions = new SizeF(7F, 17F);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(800, 450);
        Controls.Add(buttonTestMultiThread);
        Controls.Add(statusStrip);
        Controls.Add(buttonRefresh);
        Controls.Add(groupBoxSearch);
        Controls.Add(groupBoxPersonInfo);
        Controls.Add(dataGridViewPersons);
        Name = "FormMain";
        Text = @"人员管理系统";
        Load += FormMain_Load;
        ((System.ComponentModel.ISupportInitialize)dataGridViewPersons).EndInit();
        groupBoxPersonInfo.ResumeLayout(false);
        groupBoxPersonInfo.PerformLayout();
        groupBoxSearch.ResumeLayout(false);
        groupBoxSearch.PerformLayout();
        statusStrip.ResumeLayout(false);
        statusStrip.PerformLayout();
        ResumeLayout(false);
        PerformLayout();
    }

    #endregion

    private DataGridView dataGridViewPersons;
    private GroupBox groupBoxPersonInfo;
    private Button buttonDelete;
    private Button buttonUpdate;
    private Button buttonAdd;
    private TextBox textBoxEmail;
    private TextBox textBoxAge;
    private TextBox textBoxName;
    private Label labelEmail;
    private Label labelAge;
    private Label labelName;
    private GroupBox groupBoxSearch;
    private Button buttonSearch;
    private TextBox textBoxSearch;
    private Label labelSearch;
    private Button buttonRefresh;
    private StatusStrip statusStrip;
    private ToolStripStatusLabel toolStripStatusLabel;
    private Button buttonTestMultiThread;
}