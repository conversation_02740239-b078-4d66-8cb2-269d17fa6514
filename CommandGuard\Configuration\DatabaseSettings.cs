namespace CommandGuard.Configuration;

/// <summary>
/// 数据库设置配置类
/// 用于绑定appsettings.json中的ConnectionStrings节点配置
/// </summary>
public class DatabaseSettings
{
    /// <summary>配置节点名称</summary>
    public const string SectionName = @"ConnectionStrings";
    
    /// <summary>默认数据库连接字符串</summary>
    public string DefaultConnection { get; set; } = string.Empty;
    
    /// <summary>数据库类型（SQLite, SqlServer, MySQL等）</summary>
    public string DatabaseType { get; set; } = @"SQLite";
    
    /// <summary>是否启用自动迁移</summary>
    public bool EnableAutoMigration { get; set; } = true;
    
    /// <summary>连接超时时间（秒）</summary>
    public int ConnectionTimeout { get; set; } = 30;
}
