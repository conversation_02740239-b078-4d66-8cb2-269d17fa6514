using System.Linq.Expressions;
using Microsoft.Extensions.Logging;
using CommandGuard.Data;
using CommandGuard.Interfaces.Repositories;


namespace CommandGuard.Repositories;

/// <summary>
/// 通用仓储基类，提供基本的CRUD操作
/// </summary>
public abstract class BaseRepository<T>(AppDbContext dbContext, ILogger logger) : IRepository<T>
    where T : class
{
    #region 字段和属性

    /// <summary>数据库上下文，提供数据访问接口</summary>
    protected readonly AppDbContext DbContext = dbContext;

    /// <summary>日志记录器，用于记录操作日志</summary>
    protected readonly ILogger Logger = logger;

    /// <summary>信号量，确保多线程操作的数据一致性</summary>
    protected readonly SemaphoreSlim Semaphore = new(1, 1);

    #endregion

    #region 查询方法

    /// <summary>
    /// 根据ID获取单个实体
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public virtual async Task<T?> GetByIdAsync(int id)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"获取实体 {EntityType}，ID: {Id}", typeof(T).Name, id);
            return await DbContext.Select<T>().Where(@"Id = @id", new { id }).FirstAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"获取实体 {EntityType} 失败，ID: {Id}", typeof(T).Name, id);
            return null;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取所有实体列表
    /// </summary>
    /// <returns>实体列表</returns>
    public virtual async Task<IEnumerable<T>> GetAllAsync()
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"获取所有 {EntityType}", typeof(T).Name);
            return await DbContext.Select<T>().ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"获取所有 {EntityType} 失败", typeof(T).Name);
            return new List<T>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据条件表达式查找实体列表
    /// </summary>
    /// <param name="predicate">查询条件表达式</param>
    /// <returns>符合条件的实体列表</returns>
    public virtual async Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据条件查找 {EntityType}", typeof(T).Name);
            return await DbContext.Select<T>().Where(predicate).ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据条件查找 {EntityType} 失败", typeof(T).Name);
            return new List<T>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 检查指定ID的实体是否存在
    /// </summary>
    /// <param name="id">实体ID</param>
    /// <returns>存在返回true，否则返回false</returns>
    public virtual async Task<bool> ExistsAsync(int id)
    {
        await Semaphore.WaitAsync();
        try
        {
            return await DbContext.Select<T>().Where(@"Id = @id", new { id }).AnyAsync();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取实体总数量
    /// </summary>
    /// <returns>实体总数</returns>
    public virtual async Task<int> CountAsync()
    {
        await Semaphore.WaitAsync();
        try
        {
            return (int)await DbContext.Select<T>().CountAsync();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据条件获取实体数量
    /// </summary>
    /// <param name="predicate">查询条件表达式</param>
    /// <returns>符合条件的实体数量</returns>
    public virtual async Task<int> CountAsync(Expression<Func<T, bool>> predicate)
    {
        await Semaphore.WaitAsync();
        try
        {
            return (int)await DbContext.Select<T>().Where(predicate).CountAsync();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    #endregion

    #region 修改方法

    /// <summary>
    /// 添加新实体
    /// </summary>
    /// <param name="entity">要添加的实体对象</param>
    /// <returns>添加后的实体对象（包含自增ID）</returns>
    public virtual async Task<T> AddAsync(T entity)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"添加 {EntityType}", typeof(T).Name);
            SetTimestamps(entity, isUpdate: false);

            var result = await DbContext.Insert<T>().AppendData(entity).ExecuteInsertedAsync();
            return result.First();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"添加 {EntityType} 失败", typeof(T).Name);
            throw;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 更新实体信息
    /// </summary>
    /// <param name="entity">要更新的实体对象</param>
    /// <returns>更新后的实体对象</returns>
    public virtual async Task<T> UpdateAsync(T entity)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"更新 {EntityType}", typeof(T).Name);
            SetTimestamps(entity, isUpdate: true);

            await DbContext.Update<T>().SetSource(entity).ExecuteAffrowsAsync();
            return entity;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"更新 {EntityType} 失败", typeof(T).Name);
            throw;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据ID删除实体
    /// </summary>
    /// <param name="id">要删除的实体ID</param>
    /// <returns>删除成功返回true，否则返回false</returns>
    public virtual async Task<bool> DeleteAsync(int id)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"删除 {EntityType}，ID: {Id}", typeof(T).Name, id);
            var affectedRows = await DbContext.Delete<T>().Where(@"Id = @id", new { id }).ExecuteAffrowsAsync();
            return affectedRows > 0;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"删除 {EntityType} 失败，ID: {Id}", typeof(T).Name, id);
            return false;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 自动设置实体的时间戳字段
    /// </summary>
    /// <param name="entity">实体对象</param>
    /// <param name="isUpdate">是否为更新操作</param>
    protected virtual void SetTimestamps(T entity, bool isUpdate)
    {
        var entityType = typeof(T);
        var now = DateTime.Now;

        if (!isUpdate)
        {
            // 新增时设置创建时间
            var createdAtProperty = entityType.GetProperty(@"CreatedAt");
            if (createdAtProperty != null && createdAtProperty.PropertyType == typeof(DateTime))
            {
                createdAtProperty.SetValue(entity, now);
            }
        }

        // 新增和更新时都设置更新时间
        var updatedAtProperty = entityType.GetProperty(@"UpdatedAt");
        if (updatedAtProperty != null && updatedAtProperty.PropertyType == typeof(DateTime))
        {
            updatedAtProperty.SetValue(entity, now);
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public virtual void Dispose()
    {
        Semaphore?.Dispose();
    }

    #endregion
}