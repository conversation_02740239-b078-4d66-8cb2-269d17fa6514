namespace CommandGuard.Interfaces.Services;

/// <summary>
/// 配置服务接口，定义配置信息访问的契约
/// </summary>
public interface IConfigurationService
{
    #region 应用程序配置方法

    /// <summary>获取应用程序名称</summary>
    string GetApplicationName();

    /// <summary>获取应用程序版本</summary>
    string GetApplicationVersion();

    /// <summary>获取应用程序描述</summary>
    string GetApplicationDescription();

    /// <summary>获取开发者信息</summary>
    string GetDeveloper();

    /// <summary>获取完整的应用程序信息</summary>
    string GetApplicationInfo();

    #endregion

    #region 数据库配置方法

    /// <summary>获取数据库连接字符串</summary>
    string GetConnectionString();

    /// <summary>获取数据库类型</summary>
    string GetDatabaseType();

    /// <summary>获取连接超时时间</summary>
    int GetConnectionTimeout();

    /// <summary>检查是否启用自动迁移</summary>
    bool IsAutoMigrationEnabled();

    /// <summary>获取数据库配置摘要</summary>
    string GetDatabaseConfigSummary();

    #endregion

    #region 配置验证方法

    /// <summary>验证应用程序配置的完整性</summary>
    bool ValidateApplicationConfig();

    /// <summary>验证数据库配置的完整性</summary>
    bool ValidateDatabaseConfig();

    #endregion
}
