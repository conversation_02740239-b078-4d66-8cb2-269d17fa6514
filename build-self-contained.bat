@echo off
echo 正在构建自包含单文件版本...

REM 清理之前的构建
dotnet clean CommandGuard/CommandGuard.csproj

REM 自包含单文件发布
echo 构建自包含单文件版本...
dotnet publish CommandGuard/CommandGuard.csproj ^
    -c Release ^
    -r win-x64 ^
    --self-contained true ^
    -p:PublishSingleFile=true ^
    -p:PublishTrimmed=true ^
    -p:TrimMode=partial ^
    -p:IncludeNativeLibrariesForSelfExtract=true ^
    -p:IncludeAllContentForSelfExtract=true ^
    -p:PublishReadyToRun=true ^
    -o "publish/SelfContained"

echo.
echo 自包含构建完成！
echo 输出目录: publish/SelfContained/
echo 主文件: CommandGuard.exe (包含.NET运行时)
echo.

REM 显示文件大小
echo 文件信息:
dir "publish/SelfContained/CommandGuard.exe"

pause
