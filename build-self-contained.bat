@echo off
chcp 65001 >nul
echo Building self-contained single file version...

REM Clean previous builds
echo Cleaning previous builds...
dotnet clean CommandGuard\CommandGuard.csproj

REM Create output directory
if not exist "publish\SelfContained" mkdir "publish\SelfContained"

REM Self-contained single file publish
echo Building self-contained single file version...
dotnet publish CommandGuard\CommandGuard.csproj -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:PublishTrimmed=true -p:TrimMode=partial -p:IncludeNativeLibrariesForSelfExtract=true -p:IncludeAllContentForSelfExtract=true -p:PublishReadyToRun=true -o publish\SelfContained

echo.
echo Self-contained build completed!
echo Output directory: publish\SelfContained\
echo Main file: CommandGuard.exe (includes .NET runtime)
echo.

REM Show file information
echo File information:
if exist "publish\SelfContained\CommandGuard.exe" (
    dir "publish\SelfContained\CommandGuard.exe"
) else (
    echo CommandGuard.exe not found in output directory
)

pause
