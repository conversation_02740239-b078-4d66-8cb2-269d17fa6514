#!/bin/bash
echo "Building single file version..."

# Clean previous builds
echo "Cleaning previous builds..."
dotnet clean CommandGuard/CommandGuard.csproj

# Create output directory
mkdir -p "publish/SingleFile-Framework"

# Single file publish (framework dependent)
echo "Building single file version (requires .NET runtime)..."
dotnet publish CommandGuard/CommandGuard.csproj \
    -c Release \
    -r win-x64 \
    --self-contained false \
    -p:PublishSingleFile=true \
    -p:IncludeNativeLibrariesForSelfExtract=true \
    -p:PublishReadyToRun=false \
    -p:PublishTrimmed=false \
    -o "publish/SingleFile-Framework"

echo ""
echo "Single file build completed!"
echo "Output directory: publish/SingleFile-Framework/"
echo "Main file: CommandGuard.exe"
echo ""

# Show file information
echo "File information:"
if [ -f "publish/SingleFile-Framework/CommandGuard.exe" ]; then
    ls -lh "publish/SingleFile-Framework/CommandGuard.exe"
else
    echo "CommandGuard.exe not found in output directory"
fi

read -p "Press any key to continue..."
