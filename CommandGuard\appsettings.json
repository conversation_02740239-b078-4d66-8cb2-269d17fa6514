{"ConnectionStrings": {"DefaultConnection": "Data Source=database.db"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "File", "Args": {"path": "logs/app-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}, "Application": {"Name": "WinFormsAppDemo", "Version": "1.0.0", "Description": "基于.NET 8的现代化WinForms应用程序示例", "Developer": "开发团队"}}