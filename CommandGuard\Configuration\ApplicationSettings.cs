namespace CommandGuard.Configuration;

/// <summary>
/// 应用程序设置配置类
/// 用于绑定appsettings.json中的Application节点配置
/// </summary>
public class ApplicationSettings
{
    /// <summary>配置节点名称</summary>
    public const string SectionName = @"Application";
    
    /// <summary>应用程序名称</summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>应用程序版本</summary>
    public string Version { get; set; } = string.Empty;
    
    /// <summary>应用程序描述</summary>
    public string Description { get; set; } = string.Empty;
    
    /// <summary>开发者信息</summary>
    public string Developer { get; set; } = string.Empty;
}
