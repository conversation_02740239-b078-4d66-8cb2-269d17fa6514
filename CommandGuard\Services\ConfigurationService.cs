using CommandGuard.Configuration;
using CommandGuard.Interfaces.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace CommandGuard.Services;

/// <summary>
/// 配置服务类，演示如何使用强类型配置
/// 提供应用程序配置信息的访问和管理
/// </summary>
public class ConfigurationService : IConfigurationService
{
    #region 字段

    private readonly ApplicationSettings _applicationSettings;
    private readonly DatabaseSettings _databaseSettings;
    private readonly ILogger<ConfigurationService> _logger;

    #endregion

    #region 构造函数

    /// <summary>
    /// 构造函数，注入配置选项和日志服务
    /// </summary>
    /// <param name="applicationOptions">应用程序配置选项</param>
    /// <param name="databaseOptions">数据库配置选项</param>
    /// <param name="logger">日志记录器</param>
    public ConfigurationService(
        IOptions<ApplicationSettings> applicationOptions,
        IOptions<DatabaseSettings> databaseOptions,
        ILogger<ConfigurationService> logger)
    {
        _applicationSettings = applicationOptions.Value;
        _databaseSettings = databaseOptions.Value;
        _logger = logger;
    }

    #endregion

    #region 应用程序配置方法

    /// <summary>
    /// 获取应用程序名称
    /// </summary>
    /// <returns>应用程序名称</returns>
    public string GetApplicationName()
    {
        _logger.LogDebug(@"获取应用程序名称: {Name}", _applicationSettings.Name);
        return _applicationSettings.Name;
    }

    /// <summary>
    /// 获取应用程序版本
    /// </summary>
    /// <returns>应用程序版本</returns>
    public string GetApplicationVersion()
    {
        _logger.LogDebug(@"获取应用程序版本: {Version}", _applicationSettings.Version);
        return _applicationSettings.Version;
    }

    /// <summary>
    /// 获取应用程序描述
    /// </summary>
    /// <returns>应用程序描述</returns>
    public string GetApplicationDescription()
    {
        _logger.LogDebug(@"获取应用程序描述: {Description}", _applicationSettings.Description);
        return _applicationSettings.Description;
    }

    /// <summary>
    /// 获取开发者信息
    /// </summary>
    /// <returns>开发者信息</returns>
    public string GetDeveloper()
    {
        _logger.LogDebug(@"获取开发者信息: {Developer}", _applicationSettings.Developer);
        return _applicationSettings.Developer;
    }

    /// <summary>
    /// 获取完整的应用程序信息
    /// </summary>
    /// <returns>格式化的应用程序信息字符串</returns>
    public string GetApplicationInfo()
    {
        var info = $@"{_applicationSettings.Name} v{_applicationSettings.Version}
{_applicationSettings.Description}
开发者: {_applicationSettings.Developer}";
        
        _logger.LogInformation(@"获取完整应用程序信息");
        return info;
    }

    #endregion

    #region 数据库配置方法

    /// <summary>
    /// 获取数据库连接字符串
    /// </summary>
    /// <returns>数据库连接字符串</returns>
    public string GetConnectionString()
    {
        _logger.LogDebug(@"获取数据库连接字符串");
        return _databaseSettings.DefaultConnection;
    }

    /// <summary>
    /// 获取数据库类型
    /// </summary>
    /// <returns>数据库类型</returns>
    public string GetDatabaseType()
    {
        _logger.LogDebug(@"获取数据库类型: {Type}", _databaseSettings.DatabaseType);
        return _databaseSettings.DatabaseType;
    }

    /// <summary>
    /// 获取连接超时时间
    /// </summary>
    /// <returns>连接超时时间（秒）</returns>
    public int GetConnectionTimeout()
    {
        _logger.LogDebug(@"获取连接超时时间: {Timeout}秒", _databaseSettings.ConnectionTimeout);
        return _databaseSettings.ConnectionTimeout;
    }

    /// <summary>
    /// 检查是否启用自动迁移
    /// </summary>
    /// <returns>是否启用自动迁移</returns>
    public bool IsAutoMigrationEnabled()
    {
        _logger.LogDebug(@"检查自动迁移状态: {Enabled}", _databaseSettings.EnableAutoMigration);
        return _databaseSettings.EnableAutoMigration;
    }

    /// <summary>
    /// 获取数据库配置摘要
    /// </summary>
    /// <returns>数据库配置摘要信息</returns>
    public string GetDatabaseConfigSummary()
    {
        var summary = $@"数据库类型: {_databaseSettings.DatabaseType}
连接超时: {_databaseSettings.ConnectionTimeout}秒
自动迁移: {(_databaseSettings.EnableAutoMigration ? "启用" : "禁用")}";
        
        _logger.LogInformation(@"获取数据库配置摘要");
        return summary;
    }

    #endregion

    #region 配置验证方法

    /// <summary>
    /// 验证应用程序配置的完整性
    /// </summary>
    /// <returns>配置验证结果</returns>
    public bool ValidateApplicationConfig()
    {
        var isValid = !string.IsNullOrWhiteSpace(_applicationSettings.Name) &&
                     !string.IsNullOrWhiteSpace(_applicationSettings.Version);
        
        _logger.LogInformation(@"应用程序配置验证结果: {IsValid}", isValid);
        return isValid;
    }

    /// <summary>
    /// 验证数据库配置的完整性
    /// </summary>
    /// <returns>配置验证结果</returns>
    public bool ValidateDatabaseConfig()
    {
        var isValid = !string.IsNullOrWhiteSpace(_databaseSettings.DefaultConnection) &&
                     !string.IsNullOrWhiteSpace(_databaseSettings.DatabaseType) &&
                     _databaseSettings.ConnectionTimeout > 0;
        
        _logger.LogInformation(@"数据库配置验证结果: {IsValid}", isValid);
        return isValid;
    }

    #endregion
}
