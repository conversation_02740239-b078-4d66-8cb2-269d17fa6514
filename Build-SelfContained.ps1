# PowerShell script to build self-contained single file version
Write-Host "Building self-contained single file version..." -ForegroundColor Green

# Set encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# Clean previous builds
Write-Host "Cleaning previous builds..." -ForegroundColor Yellow
dotnet clean CommandGuard\CommandGuard.csproj

# Create output directory
$outputDir = "publish\SelfContained"
if (!(Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
}

# Self-contained single file publish
Write-Host "Building self-contained single file version..." -ForegroundColor Yellow
$publishArgs = @(
    "publish"
    "CommandGuard\CommandGuard.csproj"
    "-c", "Release"
    "-r", "win-x64"
    "--self-contained", "true"
    "-p:PublishSingleFile=true"
    "-p:PublishTrimmed=true"
    "-p:TrimMode=partial"
    "-p:IncludeNativeLibrariesForSelfExtract=true"
    "-p:IncludeAllContentForSelfExtract=true"
    "-p:PublishReadyToRun=true"
    "-o", $outputDir
)

& dotnet @publishArgs

if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "Self-contained build completed!" -ForegroundColor Green
    Write-Host "Output directory: $outputDir" -ForegroundColor Cyan
    Write-Host "Main file: CommandGuard.exe (includes .NET runtime)" -ForegroundColor Cyan
    Write-Host ""
    
    # Show file information
    $exePath = Join-Path $outputDir "CommandGuard.exe"
    if (Test-Path $exePath) {
        $fileInfo = Get-Item $exePath
        Write-Host "File information:" -ForegroundColor Yellow
        Write-Host "Size: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" -ForegroundColor White
        Write-Host "Created: $($fileInfo.CreationTime)" -ForegroundColor White
    } else {
        Write-Host "CommandGuard.exe not found in output directory" -ForegroundColor Red
    }
} else {
    Write-Host "Build failed!" -ForegroundColor Red
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
