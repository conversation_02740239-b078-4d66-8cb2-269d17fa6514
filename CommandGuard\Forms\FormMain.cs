using CommandGuard.Interfaces.Services;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Forms;

/// <summary>
/// 主窗体类，提供Person实体的增删改查界面操作
/// 使用依赖注入获取服务层，实现UI与业务逻辑分离
/// </summary>
public partial class FormMain : Form
{
    #region 字段

    /// <summary>人员服务接口，处理业务逻辑</summary>
    private readonly IPersonService _personService;

    /// <summary>日志记录器</summary>
    private readonly ILogger<FormMain> _logger;

    /// <summary>当前选中的人员对象</summary>
    private Person? _selectedPerson;

    #endregion

    #region 构造函数和初始化

    /// <summary>
    /// 构造函数，通过依赖注入初始化服务
    /// </summary>
    /// <param name="personService">人员服务接口</param>
    /// <param name="logger">日志记录器</param>
    public FormMain(IPersonService personService, ILogger<FormMain> logger)
    {
        _personService = personService;
        _logger = logger;
        InitializeComponent();
    }

    /// <summary>
    /// 窗体加载事件处理
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private async void FormMain_Load(object sender, EventArgs e)
    {
        try
        {
            _logger.LogInformation(@"主窗体加载");
            await LoadPersonsAsync();
            UpdateStatusLabel(@"就绪");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"主窗体加载失败");
            MessageBox.Show(@$"加载数据失败: {ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    #endregion

    #region 数据加载和显示

    /// <summary>
    /// 加载并显示所有人员数据到表格
    /// </summary>
    private async Task LoadPersonsAsync()
    {
        try
        {
            UpdateStatusLabel(@"正在加载数据...");
            // 从服务层获取所有人员数据
            var persons = await _personService.GetAllPersonsAsync();

            // 绑定数据到表格控件
            dataGridViewPersons.DataSource = persons.ToList();

            // 隐藏不需要在界面显示的时间戳列
            if (dataGridViewPersons.Columns[@"CreatedAt"] != null)
                dataGridViewPersons.Columns[@"CreatedAt"].Visible = false;
            if (dataGridViewPersons.Columns[@"UpdatedAt"] != null)
                dataGridViewPersons.Columns[@"UpdatedAt"].Visible = false;

            // 设置用户友好的列标题
            if (dataGridViewPersons.Columns[@"Id"] != null)
                dataGridViewPersons.Columns[@"Id"].HeaderText = @"ID";
            if (dataGridViewPersons.Columns[@"Name"] != null)
                dataGridViewPersons.Columns[@"Name"].HeaderText = @"姓名";
            if (dataGridViewPersons.Columns[@"Age"] != null)
                dataGridViewPersons.Columns[@"Age"].HeaderText = @"年龄";
            if (dataGridViewPersons.Columns[@"Email"] != null)
                dataGridViewPersons.Columns[@"Email"].HeaderText = @"邮箱";

            // 更新状态栏显示记录总数
            var count = await _personService.GetPersonCountAsync();
            UpdateStatusLabel(@$"共 {count} 条记录");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"加载人员数据失败");
            throw;
        }
    }

    /// <summary>
    /// 表格选择变化事件处理
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void DataGridViewPersons_SelectionChanged(object sender, EventArgs e)
    {
        if (dataGridViewPersons.SelectedRows.Count > 0)
        {
            var selectedRow = dataGridViewPersons.SelectedRows[0];
            _selectedPerson = selectedRow.DataBoundItem as Person;

            if (_selectedPerson != null)
            {
                textBoxName.Text = _selectedPerson.Name;
                textBoxAge.Text = _selectedPerson.Age.ToString();
                textBoxEmail.Text = _selectedPerson.Email;

                buttonUpdate.Enabled = true;
                buttonDelete.Enabled = true;
            }
        }
        else
        {
            ClearForm();
            buttonUpdate.Enabled = false;
            buttonDelete.Enabled = false;
        }
    }

    #endregion

    #region 按钮事件处理

    /// <summary>添加按钮点击事件处理</summary>
    private async void ButtonAdd_Click(object sender, EventArgs e)
    {
        try
        {
            if (!ValidateInput())
                return;

            var person = new Person
            {
                Name = textBoxName.Text.Trim(),
                Age = int.Parse(textBoxAge.Text.Trim()),
                Email = textBoxEmail.Text.Trim()
            };

            UpdateStatusLabel(@"正在添加人员...");
            await _personService.CreatePersonAsync(person);

            _logger.LogInformation(@"成功添加人员: {Name}", person.Name);
            MessageBox.Show(@"添加成功!", @"信息", MessageBoxButtons.OK, MessageBoxIcon.Information);

            ClearForm();
            await LoadPersonsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"添加人员失败");
            MessageBox.Show(@$"添加失败: {ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            UpdateStatusLabel(@"添加失败");
        }
    }

    /// <summary>更新按钮点击事件处理</summary>
    private async void ButtonUpdate_Click(object sender, EventArgs e)
    {
        try
        {
            if (_selectedPerson == null)
            {
                MessageBox.Show(@"请先选择要更新的人员", @"提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!ValidateInput())
                return;

            _selectedPerson.Name = textBoxName.Text.Trim();
            _selectedPerson.Age = int.Parse(textBoxAge.Text.Trim());
            _selectedPerson.Email = textBoxEmail.Text.Trim();

            UpdateStatusLabel(@"正在更新人员...");
            await _personService.UpdatePersonAsync(_selectedPerson);

            _logger.LogInformation(@"成功更新人员: {Id}", _selectedPerson.Id);
            MessageBox.Show(@"更新成功!", @"信息", MessageBoxButtons.OK, MessageBoxIcon.Information);

            await LoadPersonsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"更新人员失败");
            MessageBox.Show(@$"更新失败: {ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            UpdateStatusLabel(@"更新失败");
        }
    }

    /// <summary>删除按钮点击事件处理</summary>
    private async void ButtonDelete_Click(object sender, EventArgs e)
    {
        try
        {
            if (_selectedPerson == null)
            {
                MessageBox.Show(@"请先选择要删除的人员", @"提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show(@$"确定要删除人员 '{_selectedPerson.Name}' 吗？",
                @"确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result != DialogResult.Yes)
                return;

            UpdateStatusLabel(@"正在删除人员...");
            var success = await _personService.DeletePersonAsync(_selectedPerson.Id);

            if (success)
            {
                _logger.LogInformation(@"成功删除人员: {Id}", _selectedPerson.Id);
                MessageBox.Show(@"删除成功!", @"信息", MessageBoxButtons.OK, MessageBoxIcon.Information);

                ClearForm();
                await LoadPersonsAsync();
            }
            else
            {
                MessageBox.Show(@"删除失败", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                UpdateStatusLabel(@"删除失败");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"删除人员失败");
            MessageBox.Show(@$"删除失败: {ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            UpdateStatusLabel(@"删除失败");
        }
    }

    /// <summary>搜索按钮点击事件处理</summary>
    private async void ButtonSearch_Click(object sender, EventArgs e)
    {
        try
        {
            var searchText = textBoxSearch.Text.Trim();
            if (string.IsNullOrEmpty(searchText))
            {
                await LoadPersonsAsync();
                return;
            }

            UpdateStatusLabel(@"正在搜索...");
            var persons = await _personService.SearchPersonsByNameAsync(searchText);

            dataGridViewPersons.DataSource = persons.ToList();
            UpdateStatusLabel(@$"找到 {persons.Count()} 条记录");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"搜索失败");
            MessageBox.Show(@$"搜索失败: {ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            UpdateStatusLabel(@"搜索失败");
        }
    }

    /// <summary>刷新按钮点击事件处理</summary>
    private async void ButtonRefresh_Click(object sender, EventArgs e)
    {
        try
        {
            textBoxSearch.Clear();
            await LoadPersonsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"刷新失败");
            MessageBox.Show(@$"刷新失败: {ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>多线程并发测试，验证数据一致性</summary>
    private async void ButtonTestMultiThread_Click(object sender, EventArgs e)
    {
        try
        {
            UpdateStatusLabel(@"正在进行多线程测试...");
            buttonTestMultiThread.Enabled = false;

            // 创建10个并发任务来测试数据库操作的线程安全性
            var tasks = new List<Task>();
            var random = new Random();

            for (int i = 0; i < 10; i++)
            {
                var taskId = i;
                tasks.Add(Task.Run(async () =>
                {
                    try
                    {
                        // 创建测试用的Person对象
                        var person = new Person
                        {
                            Name = @$"测试用户{taskId}",
                            Age = random.Next(18, 80),
                            Email = @$"test{taskId}@example.com"
                        };

                        // 并发执行数据库插入操作
                        await _personService.CreatePersonAsync(person);
                        _logger.LogInformation(@"多线程测试 - 任务 {TaskId} 完成", taskId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, @"多线程测试 - 任务 {TaskId} 失败", taskId);
                    }
                }));
            }

            // 等待所有并发任务完成
            await Task.WhenAll(tasks);

            MessageBox.Show(@"多线程测试完成！请查看日志了解详细信息。", @"信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
            // 刷新数据显示测试结果
            await LoadPersonsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"多线程测试失败");
            MessageBox.Show(@$"多线程测试失败: {ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            buttonTestMultiThread.Enabled = true;
            UpdateStatusLabel(@"多线程测试完成");
        }
    }

    /// <summary>验证用户输入的数据</summary>
    private bool ValidateInput()
    {
        if (string.IsNullOrWhiteSpace(textBoxName.Text))
        {
            MessageBox.Show(@"请输入姓名", @"验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            textBoxName.Focus();
            return false;
        }

        if (!int.TryParse(textBoxAge.Text, out int age) || age < 0 || age > 150)
        {
            MessageBox.Show(@"请输入有效的年龄 (0-150)", @"验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            textBoxAge.Focus();
            return false;
        }

        if (string.IsNullOrWhiteSpace(textBoxEmail.Text))
        {
            MessageBox.Show(@"请输入邮箱", @"验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            textBoxEmail.Focus();
            return false;
        }

        try
        {
            var addr = new System.Net.Mail.MailAddress(textBoxEmail.Text);
            if (addr.Address != textBoxEmail.Text)
            {
                throw new FormatException();
            }
        }
        catch
        {
            MessageBox.Show(@"请输入有效的邮箱地址", @"验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            textBoxEmail.Focus();
            return false;
        }

        return true;
    }

    /// <summary>清空表单输入</summary>
    private void ClearForm()
    {
        textBoxName.Clear();
        textBoxAge.Clear();
        textBoxEmail.Clear();
        _selectedPerson = null;
    }

    /// <summary>更新状态栏显示信息</summary>
    private void UpdateStatusLabel(string message)
    {
        if (InvokeRequired)
        {
            Invoke(new Action<string>(UpdateStatusLabel), message);
            return;
        }

        toolStripStatusLabel.Text = message;
        statusStrip.Refresh();
    }

    #endregion
}