using CommandGuard.Data;
using CommandGuard.Interfaces.Repositories;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Repositories;

/// <summary>
/// 人员仓储实现类，提供Person实体的数据访问操作
/// 继承BaseRepository获得基本CRUD功能，并实现Person特有的业务方法
/// </summary>
public class PersonRepository(AppDbContext dbContext, ILogger<PersonRepository> logger)
    : BaseRepository<Person>(dbContext, logger), IPersonRepository
{
    #region Person特有查询方法

    /// <summary>
    /// 根据年龄范围查询人员列表
    /// </summary>
    /// <param name="minAge">最小年龄</param>
    /// <param name="maxAge">最大年龄</param>
    /// <returns>符合年龄范围的人员列表</returns>
    public async Task<IEnumerable<Person>> GetByAgeRangeAsync(int minAge, int maxAge)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据年龄范围查找Person: {MinAge}-{MaxAge}", minAge, maxAge);
            return await DbContext.Persons.Where(p => p.Age >= minAge && p.Age <= maxAge).ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据年龄范围查找Person失败: {MinAge}-{MaxAge}", minAge, maxAge);
            return new List<Person>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据邮箱地址查询人员信息
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <returns>匹配的人员信息，未找到返回null</returns>
    public async Task<Person?> GetByEmailAsync(string email)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据邮箱查找Person: {Email}", email);
            return await DbContext.Persons.Where(p => p.Email == email).FirstAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据邮箱查找Person失败: {Email}", email);
            return null;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据姓名模糊搜索人员列表
    /// </summary>
    /// <param name="name">姓名关键字</param>
    /// <returns>包含关键字的人员列表</returns>
    public async Task<IEnumerable<Person>> SearchByNameAsync(string name)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据姓名搜索Person: {Name}", name);
            return await DbContext.Persons.Where(p => p.Name.Contains(name)).ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据姓名搜索Person失败: {Name}", name);
            return new List<Person>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    #endregion
}