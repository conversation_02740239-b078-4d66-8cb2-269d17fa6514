﻿using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

/// <summary>
/// 人员实体类，对应数据库中的Persons表
/// 包含人员的基本信息和时间戳字段
/// </summary>
[Table(Name = "Persons")]
public class Person
{
    /// <summary>主键ID，自增长</summary>
    [Column(IsIdentity = true, IsPrimary = true)]
    public int Id { get; set; }

    /// <summary>姓名，最大长度100字符</summary>
    [Column(StringLength = 100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>年龄</summary>
    [Column]
    public int Age { get; set; }

    /// <summary>邮箱地址，最大长度200字符，应保持唯一性</summary>
    [Column(StringLength = 200)]
    public string Email { get; set; } = string.Empty;

    /// <summary>创建时间</summary>
    [Column]
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>最后更新时间</summary>
    [Column]
    public DateTime UpdatedAt { get; set; } = DateTime.Now;
}