@echo off
echo 正在构建单文件版本...

REM 清理之前的构建
dotnet clean CommandGuard/CommandGuard.csproj

REM 单文件发布 (依赖框架)
echo 构建单文件版本 (依赖.NET运行时)...
dotnet publish CommandGuard/CommandGuard.csproj ^
    -c Release ^
    -r win-x64 ^
    --self-contained false ^
    -p:PublishSingleFile=true ^
    -p:IncludeNativeLibrariesForSelfExtract=true ^
    -p:PublishReadyToRun=false ^
    -p:PublishTrimmed=false ^
    -o "publish/SingleFile-Framework"

echo.
echo 单文件构建完成！
echo 输出目录: publish/SingleFile-Framework/
echo 主文件: CommandGuard.exe
echo.

REM 显示文件大小
echo 文件信息:
dir "publish/SingleFile-Framework/CommandGuard.exe"

pause
