@echo off
chcp 65001 >nul
echo Building single file version...

REM Clean previous builds
echo Cleaning previous builds...
dotnet clean CommandGuard\CommandGuard.csproj

REM Create output directory
if not exist "publish\SingleFile-Framework" mkdir "publish\SingleFile-Framework"

REM Single file publish (framework dependent)
echo Building single file version (requires .NET runtime)...
dotnet publish CommandGuard\CommandGuard.csproj -c Release -r win-x64 --self-contained false -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true -p:PublishReadyToRun=false -p:PublishTrimmed=false -o publish\SingleFile-Framework

echo.
echo Single file build completed!
echo Output directory: publish\SingleFile-Framework\
echo Main file: CommandGuard.exe
echo.

REM Show file information
echo File information:
if exist "publish\SingleFile-Framework\CommandGuard.exe" (
    dir "publish\SingleFile-Framework\CommandGuard.exe"
) else (
    echo CommandGuard.exe not found in output directory
)

pause
