@echo off
chcp 65001 >nul
echo Cleaning build files...

REM Delete bin and obj directories
if exist "CommandGuard\bin" (
    echo Deleting bin directory...
    rmdir /s /q "CommandGuard\bin"
)

if exist "CommandGuard\obj" (
    echo Deleting obj directory...
    rmdir /s /q "CommandGuard\obj"
)

REM Delete publish directory
if exist "publish" (
    echo Deleting publish directory...
    rmdir /s /q "publish"
)

REM Delete log files
if exist "logs" (
    echo Deleting logs directory...
    rmdir /s /q "logs"
)

REM Delete database file
if exist "database.db" (
    echo Deleting database file...
    del "database.db"
)

echo Cleanup completed!
pause
