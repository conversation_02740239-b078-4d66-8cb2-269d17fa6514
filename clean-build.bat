@echo off
echo 正在清理构建文件...

REM 删除bin和obj目录
if exist "CommandGuard\bin" (
    echo 删除 bin 目录...
    rmdir /s /q "CommandGuard\bin"
)

if exist "CommandGuard\obj" (
    echo 删除 obj 目录...
    rmdir /s /q "CommandGuard\obj"
)

REM 删除发布目录
if exist "publish" (
    echo 删除 publish 目录...
    rmdir /s /q "publish"
)

REM 删除日志文件
if exist "logs" (
    echo 删除 logs 目录...
    rmdir /s /q "logs"
)

REM 删除数据库文件
if exist "database.db" (
    echo 删除数据库文件...
    del "database.db"
)

echo 清理完成！
pause
