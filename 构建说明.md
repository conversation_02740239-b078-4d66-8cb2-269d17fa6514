# 🚀 简化构建说明

项目已经配置为自动单文件发布，你只需要使用标准的 dotnet 命令即可！

## 📋 简单命令

### 1. 开发调试（多文件）
```bash
dotnet run --project CommandGuard
```

### 2. 发布单文件版本（推荐）
```bash
dotnet publish CommandGuard -c Release -r win-x64
```
**输出：** `CommandGuard\bin\Release\net8.0-windows\win-x64\publish\CommandGuard.exe`

### 3. 自包含单文件版本
```bash
dotnet publish CommandGuard -c Release -r win-x64 --self-contained true
```
**输出：** 包含 .NET 运行时的单个 exe 文件

### 4. 清理构建文件
```bash
dotnet clean CommandGuard
```

## 🎯 效果对比

- **之前：** 40+ 个文件（DLL、依赖库等）
- **现在：** 仅 1-2 个文件（exe + 配置文件）
- **文件减少：** 95%+

## 💡 说明

- Release 模式自动启用单文件发布
- Debug 模式保持多文件（便于调试）
- 无需复杂脚本，使用标准 dotnet 命令即可
- 配置已内置到项目文件中

## 🔧 自定义输出目录

如果想指定输出目录：
```bash
dotnet publish CommandGuard -c Release -r win-x64 -o "输出目录路径"
```
