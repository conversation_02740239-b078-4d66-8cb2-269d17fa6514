using CommandGuard.Models;

namespace CommandGuard.Interfaces.Services;

public interface IPersonService
{
    Task<Person?> GetPersonByIdAsync(int id);
    Task<IEnumerable<Person>> GetAllPersonsAsync();
    Task<Person> CreatePersonAsync(Person person);
    Task<Person> UpdatePersonAsync(Person person);
    Task<bool> DeletePersonAsync(int id);
    Task<IEnumerable<Person>> SearchPersonsByNameAsync(string name);
    Task<IEnumerable<Person>> GetPersonsByAgeRangeAsync(int minAge, int maxAge);
    Task<Person?> GetPersonByEmailAsync(string email);
    Task<bool> IsEmailUniqueAsync(string email, int? excludeId = null);
    Task<int> GetPersonCountAsync();
}
